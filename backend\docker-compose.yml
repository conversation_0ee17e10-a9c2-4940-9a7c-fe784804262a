version: '3'

services:
  image-embedding:
    build:
      context: ./ImageEmbedding
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    volumes:
      - ./ImageEmbedding:/app
    environment:
      - CUDA_VISIBLE_DEVICES=""
    restart: unless-stopped

  omni-parser:
    build:
      context: ./OmniParser
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./OmniParser:/app
      - ./OmniParser/weights:/app/weights
    environment:
      - CUDA_VISIBLE_DEVICES=""
    restart: unless-stopped

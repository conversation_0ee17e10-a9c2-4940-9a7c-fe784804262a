#!/usr/bin/env python3
"""
测试各种服务连接的脚本
"""

import sys
import config
from data.graph_db import Neo4jDatabase
from tool.screen_content import list_all_devices, get_device_size
import requests

def test_neo4j():
    """测试Neo4j连接"""
    print("🔍 测试Neo4j Aura连接...")
    try:
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        print("✅ Neo4j Aura连接成功!")
        db.close()
        return True
    except Exception as e:
        print(f"❌ Neo4j连接失败: {str(e)}")
        return False

def test_adb():
    """测试ADB连接"""
    print("🔍 测试ADB设备连接...")
    try:
        devices = list_all_devices()
        if devices:
            print(f"✅ ADB连接成功! 发现设备: {devices}")
            # 测试获取设备信息
            for device in devices:
                try:
                    size_info = get_device_size.invoke({"device": device})
                    print(f"   设备 {device} 屏幕尺寸: {size_info}")
                except Exception as e:
                    print(f"   获取设备 {device} 信息失败: {str(e)}")
            return True
        else:
            print("❌ 未发现ADB设备")
            return False
    except Exception as e:
        print(f"❌ ADB连接失败: {str(e)}")
        return False

def test_backend_services():
    """测试后端服务"""
    print("🔍 测试后端服务连接...")
    
    # 测试特征提取服务
    try:
        response = requests.get(f"{config.Feature_URI}/available_models", timeout=5)
        if response.status_code == 200:
            print("✅ 特征提取服务 (8001端口) 连接成功!")
        else:
            print(f"⚠️ 特征提取服务响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 特征提取服务连接失败: {str(e)}")
    
    # 测试屏幕解析服务
    try:
        response = requests.get(f"{config.Omni_URI}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ 屏幕解析服务 (8000端口) 连接成功!")
        else:
            print(f"⚠️ 屏幕解析服务响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 屏幕解析服务连接失败: {str(e)}")

def test_llm():
    """测试LLM连接"""
    print("🔍 测试Qwen LLM连接...")
    try:
        from langchain_openai import ChatOpenAI
        from pydantic import SecretStr
        
        model = ChatOpenAI(
            openai_api_base=config.LLM_BASE_URL,
            openai_api_key=SecretStr(config.LLM_API_KEY),
            model_name=config.LLM_MODEL,
            request_timeout=config.LLM_REQUEST_TIMEOUT,
            max_retries=config.LLM_MAX_RETRIES,
            max_tokens=100,
        )
        
        response = model.invoke("你好，请回复'连接成功'")
        print(f"✅ Qwen LLM连接成功! 响应: {response.content}")
        return True
    except Exception as e:
        print(f"❌ Qwen LLM连接失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 AppAgentX 连接测试开始...")
    print("=" * 50)
    
    results = []
    
    # 测试各个服务
    results.append(("Neo4j Aura", test_neo4j()))
    results.append(("ADB设备", test_adb()))
    results.append(("Qwen LLM", test_llm()))
    
    print("\n🔍 测试后端服务 (可选)...")
    test_backend_services()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for service, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {service}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有核心服务连接正常! 可以启动项目了!")
        print("\n启动命令: python demo.py")
    else:
        print("⚠️ 部分服务连接失败，请检查配置")
        print("\n注意:")
        print("- 后端服务 (Docker) 是可选的，不影响基本功能")
        print("- 如果只有后端服务失败，仍可以启动项目进行基本测试")
    
    return all_passed

if __name__ == "__main__":
    main()

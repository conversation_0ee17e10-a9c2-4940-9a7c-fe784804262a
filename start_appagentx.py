#!/usr/bin/env python3
"""
AppAgentX 启动脚本
"""

import sys
import os
import subprocess
import time

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查Python依赖...")
    try:
        import gradio
        import neo4j
        import langchain
        import langgraph
        import langchain_openai
        print("✅ 核心依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

def test_basic_connections():
    """测试基本连接"""
    print("🔍 测试基本连接...")
    
    # 测试配置文件
    try:
        import config
        print(f"✅ 配置文件加载成功")
        print(f"   - LLM模型: {config.LLM_MODEL}")
        print(f"   - Neo4j URI: {config.Neo4j_URI}")
        print(f"   - ADB路径: {config.ADB_PATH}")
        return True
    except Exception as e:
        print(f"❌ 配置文件错误: {e}")
        return False

def start_demo():
    """启动演示程序"""
    print("🚀 启动AppAgentX演示程序...")
    try:
        # 直接导入并运行demo
        import demo
        print("✅ Demo模块导入成功，Gradio界面应该正在启动...")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 AppAgentX 启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 请先安装依赖: pip install -r requirements.txt")
        return False
    
    # 测试连接
    if not test_basic_connections():
        print("\n❌ 配置文件有问题，请检查config.py")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 准备工作完成，正在启动...")
    
    # 启动demo
    if start_demo():
        print("\n✅ 启动成功!")
        print("📱 请在浏览器中访问显示的URL")
        print("🔧 如果遇到问题，请检查:")
        print("   1. Neo4j Aura连接是否正常")
        print("   2. Android设备是否已连接")
        print("   3. Docker后端服务是否运行 (可选)")
        return True
    else:
        print("\n❌ 启动失败，请检查错误信息")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 意外错误: {e}")
        import traceback
        traceback.print_exc()

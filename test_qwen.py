#!/usr/bin/env python3
"""
测试Qwen API连接
"""

import sys
import traceback

def test_qwen_connection():
    """测试Qwen API连接"""
    print("🔍 测试Qwen API连接...")
    
    try:
        # 导入配置
        import config
        print(f"✅ 配置加载成功")
        print(f"   API URL: {config.LLM_BASE_URL}")
        print(f"   模型: {config.LLM_MODEL}")
        print(f"   API Key: {config.LLM_API_KEY[:20]}...")
        
        # 测试LangChain OpenAI连接
        from langchain_openai import ChatOpenAI
        from pydantic import SecretStr
        
        print("\n🔗 初始化ChatOpenAI...")
        model = ChatOpenAI(
            openai_api_base=config.LLM_BASE_URL,
            openai_api_key=SecretStr(config.LLM_API_KEY),
            model_name=config.LLM_MODEL,
            request_timeout=config.LLM_REQUEST_TIMEOUT,
            max_retries=config.LLM_MAX_RETRIES,
            max_tokens=100,
        )
        print("✅ ChatOpenAI初始化成功")
        
        # 发送测试请求
        print("\n📤 发送测试请求...")
        response = model.invoke("你好，请简单回复'连接成功'")
        print(f"✅ Qwen API连接成功!")
        print(f"📥 响应: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Qwen API连接失败: {str(e)}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        return False

def test_neo4j_connection():
    """测试Neo4j连接"""
    print("\n🔍 测试Neo4j连接...")
    
    try:
        import config
        from data.graph_db import Neo4jDatabase
        
        print(f"   URI: {config.Neo4j_URI}")
        print(f"   用户名: {config.Neo4j_AUTH[0]}")
        
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        print("✅ Neo4j连接成功!")
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Neo4j连接失败: {str(e)}")
        return False

def test_adb_connection():
    """测试ADB连接"""
    print("\n🔍 测试ADB连接...")
    
    try:
        from tool.screen_content import list_all_devices
        
        devices = list_all_devices()
        if devices:
            print(f"✅ ADB连接成功! 发现设备: {devices}")
            return True
        else:
            print("❌ 未发现ADB设备")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 AppAgentX 核心服务连接测试")
    print("=" * 50)
    
    results = []
    
    # 测试各个服务
    results.append(("Qwen LLM", test_qwen_connection()))
    results.append(("Neo4j数据库", test_neo4j_connection()))
    results.append(("ADB设备", test_adb_connection()))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for service, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {service}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有核心服务连接正常!")
        print("\n✨ 可以启动AppAgentX了:")
        print("   python demo.py")
    else:
        print("⚠️ 部分服务连接失败")
        print("\n🔧 请检查:")
        print("   1. 网络连接是否正常")
        print("   2. API密钥是否正确")
        print("   3. 设备是否已连接")
    
    return all_passed

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        traceback.print_exc()

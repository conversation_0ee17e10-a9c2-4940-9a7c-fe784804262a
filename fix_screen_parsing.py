#!/usr/bin/env python3
"""
修复屏幕解析问题 - 创建备用方案
"""

import sys
import traceback

def create_fallback_screen_parser():
    """创建备用屏幕解析器"""
    print("🔧 创建备用屏幕解析方案...")
    
    fallback_code = '''
import json
import base64
from PIL import Image
import io

def fallback_parse_screen(image_path):
    """
    备用屏幕解析函数 - 当OmniParser不可用时使用
    返回基本的屏幕信息，允许基本操作
    """
    try:
        # 读取图片获取基本信息
        with Image.open(image_path) as img:
            width, height = img.size
        
        # 返回基本的屏幕布局信息
        # 这是一个简化的解析结果，包含常见的UI区域
        parsed_result = {
            "screen_width": width,
            "screen_height": height,
            "elements": [
                {
                    "id": "screen_center",
                    "text": "屏幕中心",
                    "bbox": [width//4, height//4, 3*width//4, 3*height//4],
                    "center": [width//2, height//2],
                    "clickable": True
                },
                {
                    "id": "top_area", 
                    "text": "顶部区域",
                    "bbox": [0, 0, width, height//4],
                    "center": [width//2, height//8],
                    "clickable": True
                },
                {
                    "id": "bottom_area",
                    "text": "底部区域", 
                    "bbox": [0, 3*height//4, width, height],
                    "center": [width//2, 7*height//8],
                    "clickable": True
                },
                {
                    "id": "left_area",
                    "text": "左侧区域",
                    "bbox": [0, height//4, width//4, 3*height//4], 
                    "center": [width//8, height//2],
                    "clickable": True
                },
                {
                    "id": "right_area",
                    "text": "右侧区域",
                    "bbox": [3*width//4, height//4, width, 3*height//4],
                    "center": [7*width//8, height//2], 
                    "clickable": True
                }
            ]
        }
        
        return parsed_result
        
    except Exception as e:
        print(f"备用解析器错误: {e}")
        return {
            "screen_width": 1080,
            "screen_height": 1920, 
            "elements": [
                {
                    "id": "default_center",
                    "text": "默认中心点",
                    "bbox": [400, 800, 680, 1120],
                    "center": [540, 960],
                    "clickable": True
                }
            ]
        }
'''
    
    return fallback_code

def patch_screen_content():
    """修补screen_content.py文件"""
    print("🔧 修补屏幕内容解析模块...")
    
    try:
        # 读取当前的screen_content.py
        with open('tool/screen_content.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有备用解析器
        if 'fallback_parse_screen' in content:
            print("✅ 备用解析器已存在")
            return True
        
        # 添加备用解析器到文件开头
        fallback_code = create_fallback_screen_parser()
        
        # 在imports后添加备用函数
        import_end = content.find('import config')
        if import_end != -1:
            insert_pos = content.find('\n', import_end) + 1
            new_content = content[:insert_pos] + '\n' + fallback_code + '\n' + content[insert_pos:]
            
            # 写回文件
            with open('tool/screen_content.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 备用解析器已添加到screen_content.py")
            return True
        else:
            print("❌ 无法找到合适的插入位置")
            return False
            
    except Exception as e:
        print(f"❌ 修补失败: {e}")
        return False

def create_simple_demo():
    """创建简化版演示"""
    print("🎯 创建简化版演示...")
    
    simple_demo_code = '''#!/usr/bin/env python3
"""
AppAgentX 简化版演示 - 无需Docker后端
"""

import gradio as gr
import config
from tool.screen_content import list_all_devices, capture_screenshot, perform_action

def simple_screenshot(device):
    """简单截图功能"""
    if not device:
        return "请选择设备", None
    
    try:
        img_path = capture_screenshot(device)
        return f"截图成功: {img_path}", img_path
    except Exception as e:
        return f"截图失败: {str(e)}", None

def simple_action(device, action_type, x, y, text):
    """简单操作功能"""
    if not device:
        return "请选择设备"
    
    try:
        if action_type == "点击" and x and y:
            result = perform_action(device, "tap", x=int(x), y=int(y))
            return f"点击操作完成: ({x}, {y})"
        elif action_type == "输入" and text:
            result = perform_action(device, "input", input_str=text)
            return f"输入操作完成: {text}"
        elif action_type == "返回":
            result = perform_action(device, "back")
            return "返回操作完成"
        else:
            return "请提供有效的操作参数"
    except Exception as e:
        return f"操作失败: {str(e)}"

# 创建Gradio界面
with gr.Blocks(title="AppAgentX 简化版") as demo:
    gr.Markdown("# 🤖 AppAgentX 简化版")
    gr.Markdown("### 基础设备控制功能 (无需Docker后端)")
    
    with gr.Row():
        device_dropdown = gr.Dropdown(
            choices=list_all_devices(),
            label="选择设备",
            value=list_all_devices()[0] if list_all_devices() else None
        )
        refresh_btn = gr.Button("🔄 刷新设备")
    
    with gr.Tab("📸 屏幕截图"):
        screenshot_btn = gr.Button("截取屏幕")
        screenshot_result = gr.Textbox(label="结果")
        screenshot_img = gr.Image(label="屏幕截图")
        
        screenshot_btn.click(
            simple_screenshot,
            inputs=device_dropdown,
            outputs=[screenshot_result, screenshot_img]
        )
    
    with gr.Tab("🎮 设备操作"):
        with gr.Row():
            action_type = gr.Dropdown(
                choices=["点击", "输入", "返回"],
                label="操作类型",
                value="点击"
            )
        
        with gr.Row():
            x_coord = gr.Number(label="X坐标", value=540)
            y_coord = gr.Number(label="Y坐标", value=960)
        
        text_input = gr.Textbox(label="输入文本", placeholder="仅在输入操作时需要")
        
        action_btn = gr.Button("执行操作")
        action_result = gr.Textbox(label="操作结果")
        
        action_btn.click(
            simple_action,
            inputs=[device_dropdown, action_type, x_coord, y_coord, text_input],
            outputs=action_result
        )
    
    def refresh_devices():
        return gr.Dropdown(choices=list_all_devices())
    
    refresh_btn.click(refresh_devices, outputs=device_dropdown)

if __name__ == "__main__":
    demo.launch(server_name="127.0.0.1", server_port=7400, share=True)
'''
    
    with open('simple_demo.py', 'w', encoding='utf-8') as f:
        f.write(simple_demo_code)
    
    print("✅ 简化版演示已创建: simple_demo.py")

def main():
    """主函数"""
    print("🔧 AppAgentX 屏幕解析问题修复工具")
    print("=" * 50)
    
    print("📋 问题分析:")
    print("- Docker后端服务(端口8000)未启动")
    print("- OmniParser屏幕解析服务不可用")
    print("- 需要备用解析方案")
    
    print("\n💡 解决方案:")
    print("1. 等待Docker服务启动完成")
    print("2. 创建简化版演示程序")
    print("3. 提供基础设备控制功能")
    
    # 创建简化版演示
    create_simple_demo()
    
    print("\n🚀 临时解决方案:")
    print("运行简化版: python simple_demo.py")
    print("- 基础截图功能")
    print("- 手动设备操作")
    print("- 无需Docker后端")
    
    print("\n⏳ Docker状态:")
    print("Docker服务正在启动中，完成后可使用完整功能")

if __name__ == "__main__":
    main()
'''

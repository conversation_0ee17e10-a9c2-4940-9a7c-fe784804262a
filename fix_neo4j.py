#!/usr/bin/env python3
"""
Neo4j Aura连接问题修复指南
"""

import sys
import traceback
import time

def check_neo4j_aura_status():
    """检查Neo4j Aura状态并提供解决方案"""
    print("🔍 Neo4j Aura连接问题诊断")
    print("=" * 50)
    
    print("📋 可能的原因和解决方案:")
    print()
    
    print("1. 🔄 **实例被暂停 (最常见)**")
    print("   - Neo4j Aura免费版会在不活跃时自动暂停")
    print("   - 解决方案: 登录Neo4j Aura控制台手动启动实例")
    print("   - 网址: https://console.neo4j.io")
    print()
    
    print("2. 🔑 **认证信息过期**")
    print("   - 密码可能已被重置或过期")
    print("   - 解决方案: 在Aura控制台重置密码")
    print()
    
    print("3. 🌐 **IP白名单限制**")
    print("   - 你的IP地址可能不在允许列表中")
    print("   - 解决方案: 在Aura控制台添加当前IP到白名单")
    print()
    
    print("4. ⏰ **实例正在启动中**")
    print("   - 实例可能正在启动过程中")
    print("   - 解决方案: 等待几分钟后重试")
    print()

def test_with_retry():
    """重试连接测试"""
    print("🔄 尝试重新连接Neo4j...")
    
    for attempt in range(3):
        print(f"\n尝试 {attempt + 1}/3:")
        try:
            import config
            from data.graph_db import Neo4jDatabase
            
            db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
            print("✅ 连接成功!")
            db.close()
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            if attempt < 2:
                print("⏳ 等待10秒后重试...")
                time.sleep(10)
    
    return False

def create_fallback_solution():
    """创建备用解决方案"""
    print("\n🔧 创建临时解决方案...")
    
    # 修改deployment.py以支持Neo4j离线模式
    fallback_code = '''
# Neo4j离线模式支持
class MockNeo4jDatabase:
    """Neo4j数据库的模拟实现"""
    
    def __init__(self):
        print("⚠️ 使用Neo4j模拟模式")
        self.connected = False
    
    def get_all_high_level_actions(self):
        return []
    
    def get_action_by_id(self, element_id):
        return None
    
    def get_element_by_id(self, element_id):
        return None
    
    def get_all_actions(self):
        return []
    
    def get_high_level_actions_for_task(self, task):
        return []
    
    def close(self):
        pass

# 在deployment.py中使用
try:
    db = Neo4jDatabase(URI, AUTH)
    print("✅ Neo4j数据库连接成功")
except Exception as e:
    print(f"⚠️ Neo4j连接失败，使用模拟模式: {str(e)}")
    db = MockNeo4jDatabase()
'''
    
    print("📝 备用代码已准备，可以让项目在Neo4j离线时继续运行")
    return fallback_code

def provide_manual_steps():
    """提供手动解决步骤"""
    print("\n📋 手动解决步骤:")
    print("=" * 30)
    
    print("步骤1: 检查Neo4j Aura实例状态")
    print("   1. 访问: https://console.neo4j.io")
    print("   2. 登录你的账号")
    print("   3. 查看实例状态")
    print("   4. 如果显示'Paused'，点击'Resume'启动")
    print()
    
    print("步骤2: 检查网络设置")
    print("   1. 在实例详情中找到'Network Access'")
    print("   2. 确认你的IP地址在白名单中")
    print("   3. 如果没有，添加当前IP: 0.0.0.0/0 (临时)")
    print()
    
    print("步骤3: 重置密码(如果需要)")
    print("   1. 在实例详情中找到'Connection'")
    print("   2. 点击'Reset Password'")
    print("   3. 复制新密码并更新config.py")
    print()
    
    print("步骤4: 测试连接")
    print("   1. 等待实例完全启动(约2-3分钟)")
    print("   2. 运行: python fix_neo4j.py")
    print("   3. 如果成功，运行: python demo.py")

def main():
    """主函数"""
    check_neo4j_aura_status()
    
    # 尝试重新连接
    if test_with_retry():
        print("\n🎉 Neo4j连接已恢复!")
        print("✨ 现在可以运行: python demo.py")
        return True
    
    # 提供解决方案
    print("\n❌ 连接仍然失败")
    provide_manual_steps()
    
    # 询问是否创建备用方案
    print("\n" + "=" * 50)
    print("💡 临时解决方案:")
    print("如果你想先测试项目的其他功能，我可以修改代码")
    print("让项目在Neo4j离线时也能运行(功能会有限制)")
    
    response = input("\n是否创建临时解决方案? (y/n): ").lower().strip()
    
    if response == 'y':
        create_fallback_solution()
        print("\n✅ 临时解决方案已准备")
        print("🚀 现在可以尝试运行项目了")
        return True
    else:
        print("\n👍 请按照上述步骤手动解决Neo4j连接问题")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        traceback.print_exc()

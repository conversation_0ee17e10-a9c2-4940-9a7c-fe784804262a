#!/usr/bin/env python3
"""
最终服务状态测试
"""

import sys
import traceback

def test_all_services():
    """测试所有服务"""
    print("🎯 AppAgentX 最终服务状态测试")
    print("=" * 50)
    
    results = []
    
    # 测试Qwen LLM
    print("1. 🤖 测试Qwen LLM...")
    try:
        import config
        from langchain_openai import ChatOpenAI
        from pydantic import SecretStr
        
        model = ChatOpenAI(
            openai_api_base=config.LLM_BASE_URL,
            openai_api_key=SecretStr(config.LLM_API_KEY),
            model_name=config.LLM_MODEL,
            max_tokens=50,
        )
        response = model.invoke("测试")
        print("✅ Qwen LLM连接成功")
        results.append(("Qwen LLM", True))
    except Exception as e:
        print(f"❌ Qwen LLM失败: {str(e)}")
        results.append(("Qwen LLM", False))
    
    # 测试Neo4j
    print("\n2. 🗄️ 测试Neo4j数据库...")
    try:
        from data.graph_db import Neo4jDatabase
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        print("✅ Neo4j连接成功")
        db.close()
        results.append(("Neo4j", True))
    except Exception as e:
        print(f"❌ Neo4j失败: {str(e)}")
        results.append(("Neo4j", False))
    
    # 测试Pinecone
    print("\n3. 🔍 测试Pinecone向量数据库...")
    try:
        from data.vector_db import VectorStore
        vector_store = VectorStore(api_key=config.PINECONE_API_KEY)
        print("✅ Pinecone连接成功")
        results.append(("Pinecone", True))
    except Exception as e:
        print(f"❌ Pinecone失败: {str(e)}")
        results.append(("Pinecone", False))
    
    # 测试ADB
    print("\n4. 📱 测试ADB设备...")
    try:
        from tool.screen_content import list_all_devices
        devices = list_all_devices()
        if devices:
            print(f"✅ ADB连接成功，设备: {devices}")
            results.append(("ADB设备", True))
        else:
            print("❌ 未发现ADB设备")
            results.append(("ADB设备", False))
    except Exception as e:
        print(f"❌ ADB失败: {str(e)}")
        results.append(("ADB设备", False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 最终测试结果:")
    
    all_passed = True
    for service, passed in results:
        status = "✅ 正常" if passed else "❌ 异常"
        print(f"   {service}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有服务连接正常!")
        print("\n🚀 AppAgentX完全可用:")
        print("   - 主界面: http://127.0.0.1:7863")
        print("   - 所有功能: ✅ 可用")
        print("   - 演化学习: ✅ 可用")
        print("   - 向量搜索: ✅ 可用")
        print("   - 知识图谱: ✅ 可用")
    else:
        print("⚠️ 部分服务异常，但基本功能仍可使用")
        print("\n🔧 建议:")
        print("   - 检查异常服务的配置")
        print("   - 基本AI操作功能不受影响")
    
    return all_passed

def main():
    """主函数"""
    try:
        test_all_services()
        
        print("\n🎯 使用指南:")
        print("1. 在浏览器中访问: http://127.0.0.1:7863")
        print("2. 选择'Initialization'标签页")
        print("3. 选择设备并输入任务描述")
        print("4. 开始体验AI手机操作代理!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
验证AppAgentX配置的脚本
"""

def verify_config():
    """验证配置文件"""
    try:
        import config
        print("✅ 配置文件加载成功")
        print(f"   LLM模型: {config.LLM_MODEL}")
        print(f"   LLM URL: {config.LLM_BASE_URL}")
        print(f"   Neo4j URI: {config.Neo4j_URI}")
        print(f"   ADB路径: {config.ADB_PATH}")
        return True
    except Exception as e:
        print(f"❌ 配置文件错误: {e}")
        return False

def verify_dependencies():
    """验证依赖"""
    deps = [
        'gradio', 'neo4j', 'langchain', 'langgraph', 
        'langchain_openai', 'requests', 'PIL'
    ]
    
    missing = []
    for dep in deps:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep}")
            missing.append(dep)
    
    return len(missing) == 0

def main():
    print("🔍 AppAgentX 配置验证")
    print("=" * 40)
    
    print("\n📋 检查依赖包:")
    deps_ok = verify_dependencies()
    
    print("\n⚙️ 检查配置:")
    config_ok = verify_config()
    
    print("\n" + "=" * 40)
    if deps_ok and config_ok:
        print("🎉 所有检查通过！可以启动项目了")
        print("\n启动命令: python demo.py")
    else:
        print("⚠️ 发现问题，请检查上述错误")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
调试demo.py启动问题
"""

import sys
import traceback

def main():
    print("🔍 调试demo.py启动问题...")
    
    try:
        print("1. 导入基础模块...")
        import config
        print("✅ config导入成功")
        
        import gradio as gr
        print("✅ gradio导入成功")
        
        print("2. 导入数据模块...")
        from data.State import State
        print("✅ State导入成功")
        
        from data.data_storage import state2json, json2db
        print("✅ data_storage导入成功")
        
        print("3. 导入工具模块...")
        from tool.screen_content import list_all_devices, get_device_size
        print("✅ screen_content导入成功")
        
        print("4. 导入探索模块...")
        from explor_auto import run_task
        print("✅ explor_auto导入成功")
        
        from explor_human import single_human_explor, capture_and_parse_page
        print("✅ explor_human导入成功")
        
        print("5. 导入链模块...")
        from chain_evolve import evolve_chain_to_action
        print("✅ chain_evolve导入成功")
        
        from chain_understand import process_and_update_chain, Neo4jDatabase
        print("✅ chain_understand导入成功")
        
        print("6. 导入部署模块...")
        from deployment import run_task as deployment_run_task
        print("✅ deployment导入成功")
        
        print("\n🎉 所有模块导入成功!")
        
        # 尝试创建简单的Gradio界面
        print("7. 创建Gradio界面...")
        
        def hello(name):
            return f"Hello {name}!"
        
        with gr.Blocks() as demo:
            gr.Markdown("# AppAgentX 测试界面")
            name = gr.Textbox(label="输入名字")
            output = gr.Textbox(label="输出")
            btn = gr.Button("测试")
            btn.click(hello, inputs=name, outputs=output)
        
        print("✅ Gradio界面创建成功")
        
        print("\n🚀 启动Gradio界面...")
        demo.launch(server_name="127.0.0.1", server_port=7860, share=False)
        
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试Docker修复是否有效
"""

import subprocess
import time
import requests
import sys

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timeout"
    except Exception as e:
        return False, "", str(e)

def test_docker_services():
    """测试Docker服务"""
    print("🔧 测试Docker服务修复...")
    
    # 1. 停止现有服务
    print("1. 停止现有服务...")
    success, stdout, stderr = run_command("docker-compose down", cwd="backend")
    if success:
        print("✅ 服务已停止")
    else:
        print(f"⚠️ 停止服务警告: {stderr}")
    
    # 2. 重新构建omni-parser
    print("2. 重新构建omni-parser...")
    success, stdout, stderr = run_command("docker-compose build omni-parser", cwd="backend")
    if success:
        print("✅ omni-parser重新构建成功")
    else:
        print(f"❌ 构建失败: {stderr}")
        return False
    
    # 3. 启动服务
    print("3. 启动服务...")
    success, stdout, stderr = run_command("docker-compose up -d", cwd="backend")
    if success:
        print("✅ 服务启动命令执行成功")
    else:
        print(f"❌ 启动失败: {stderr}")
        return False
    
    # 4. 等待服务启动
    print("4. 等待服务启动...")
    for i in range(30):  # 等待最多30秒
        time.sleep(1)
        success, stdout, stderr = run_command("docker ps --format 'table {{.Names}}\\t{{.Status}}'")
        if success and "backend-omni-parser-1" in stdout and "Up" in stdout:
            print(f"✅ 服务启动成功 (等待了{i+1}秒)")
            break
        print(f"⏳ 等待中... ({i+1}/30)")
    else:
        print("❌ 服务启动超时")
        return False
    
    # 5. 检查服务日志
    print("5. 检查服务日志...")
    success, stdout, stderr = run_command("docker logs --tail 10 backend-omni-parser-1")
    if success:
        print("📋 最新日志:")
        print(stdout[-500:])  # 显示最后500字符
        
        if "Using device:" in stdout:
            print("✅ 设备检测代码正在工作")
        if "uvicorn" in stdout.lower() and "started" in stdout.lower():
            print("✅ Uvicorn服务已启动")
    
    # 6. 测试端口连接
    print("6. 测试端口8000连接...")
    for i in range(10):  # 尝试10次
        try:
            response = requests.get("http://127.0.0.1:8000/docs", timeout=5)
            if response.status_code == 200:
                print("✅ 端口8000连接成功!")
                return True
            else:
                print(f"⚠️ HTTP状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"⏳ 连接尝试 {i+1}/10...")
            time.sleep(2)
        except Exception as e:
            print(f"❌ 连接错误: {e}")
    
    print("❌ 端口8000连接失败")
    return False

def main():
    """主函数"""
    print("🎯 Docker服务修复测试")
    print("=" * 50)
    
    if test_docker_services():
        print("\n🎉 修复成功!")
        print("✅ OmniParser服务现在应该可以正常工作")
        print("🚀 可以重新测试'打开计算器'功能")
    else:
        print("\n❌ 修复失败")
        print("🔧 需要进一步调试Docker服务问题")
        
        # 显示详细的调试信息
        print("\n🔍 调试信息:")
        success, stdout, stderr = run_command("docker ps")
        if success:
            print("Docker容器状态:")
            print(stdout)
        
        success, stdout, stderr = run_command("docker logs --tail 20 backend-omni-parser-1")
        if success:
            print("\nOmniParser日志:")
            print(stdout)

if __name__ == "__main__":
    main()

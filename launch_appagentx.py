#!/usr/bin/env python3
"""
AppAgentX 启动脚本 - 完整版
"""

import sys
import traceback
import time

def test_all_connections():
    """测试所有连接"""
    print("🔍 测试所有服务连接...")
    
    try:
        import config
        
        # 测试Qwen
        print("1. 测试Qwen LLM...")
        from langchain_openai import ChatOpenAI
        from pydantic import SecretStr
        model = ChatOpenAI(
            openai_api_base=config.LLM_BASE_URL,
            openai_api_key=SecretStr(config.LLM_API_KEY),
            model_name=config.LLM_MODEL,
            max_tokens=50,
        )
        print("✅ Qwen LLM连接成功")
        
        # 测试Neo4j
        print("2. 测试Neo4j...")
        from data.graph_db import Neo4jDatabase
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        db.close()
        print("✅ Neo4j连接成功")
        
        # 测试ADB
        print("3. 测试ADB...")
        from tool.screen_content import list_all_devices
        devices = list_all_devices()
        print(f"✅ ADB连接成功，设备: {devices}")
        
        # 测试Pinecone
        print("4. 测试Pinecone...")
        from data.vector_db import VectorStore
        vector_store = VectorStore(api_key=config.PINECONE_API_KEY)
        print("✅ Pinecone连接成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        traceback.print_exc()
        return False

def launch_gradio():
    """启动Gradio界面"""
    print("\n🚀 启动AppAgentX Gradio界面...")
    
    try:
        # 导入所有必要模块
        print("📦 导入模块...")
        import gradio as gr
        import config
        from data.State import State
        from data.data_storage import state2json, json2db
        from tool.screen_content import list_all_devices, get_device_size, capture_screenshot, perform_action
        from explor_auto import run_task
        from explor_human import single_human_explor, capture_and_parse_page
        from chain_evolve import evolve_chain_to_action
        from chain_understand import process_and_update_chain, Neo4jDatabase
        from deployment import run_task as deployment_run_task
        
        print("✅ 所有模块导入成功")
        
        # 创建简化的Gradio界面
        print("🎨 创建Gradio界面...")
        
        with gr.Blocks(title="AppAgentX - AI手机操作代理") as demo:
            gr.Markdown("# 🤖 AppAgentX - AI手机操作代理")
            gr.Markdown("### 基于演化学习的智能GUI代理系统")
            
            with gr.Tab("🔧 系统状态"):
                gr.Markdown("## 系统连接状态")
                status_text = gr.Textbox(
                    value="✅ Qwen LLM: 已连接\n✅ Neo4j数据库: 已连接\n✅ ADB设备: 已连接\n✅ Pinecone向量库: 已连接",
                    label="服务状态",
                    lines=5,
                    interactive=False
                )
                
                refresh_btn = gr.Button("🔄 刷新状态")
                
                def refresh_status():
                    try:
                        devices = list_all_devices()
                        return f"✅ Qwen LLM: 已连接\n✅ Neo4j数据库: 已连接\n✅ ADB设备: {devices}\n✅ Pinecone向量库: 已连接\n\n🕐 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                    except Exception as e:
                        return f"❌ 状态检查失败: {str(e)}"
                
                refresh_btn.click(refresh_status, outputs=status_text)
            
            with gr.Tab("📱 设备控制"):
                gr.Markdown("## 设备操作测试")
                
                device_dropdown = gr.Dropdown(
                    choices=list_all_devices(),
                    label="选择设备",
                    value=list_all_devices()[0] if list_all_devices() else None
                )
                
                screenshot_btn = gr.Button("📸 截取屏幕")
                screenshot_img = gr.Image(label="屏幕截图")
                
                def take_screenshot(device):
                    if device:
                        try:
                            img_path = capture_screenshot(device)
                            return img_path
                        except Exception as e:
                            return f"截图失败: {str(e)}"
                    return "请先选择设备"
                
                screenshot_btn.click(take_screenshot, inputs=device_dropdown, outputs=screenshot_img)
            
            with gr.Tab("🎯 任务执行"):
                gr.Markdown("## AI任务执行")
                gr.Markdown("⚠️ 完整功能正在加载中，请稍后...")
                
                task_input = gr.Textbox(label="任务描述", placeholder="例如：打开设置")
                execute_btn = gr.Button("🚀 执行任务")
                result_output = gr.Textbox(label="执行结果", lines=5)
                
                def execute_task(task):
                    return f"任务 '{task}' 已接收，完整功能即将可用..."
                
                execute_btn.click(execute_task, inputs=task_input, outputs=result_output)
        
        print("✅ Gradio界面创建成功")
        
        # 启动界面
        print("🌐 启动Web界面...")
        demo.launch(
            server_name="127.0.0.1",
            server_port=7862,
            share=False,
            show_error=True,
            quiet=False
        )
        
    except Exception as e:
        print(f"❌ Gradio启动失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 AppAgentX 完整版启动器")
    print("=" * 50)
    
    # 测试连接
    if not test_all_connections():
        print("\n❌ 服务连接测试失败，请检查配置")
        return False
    
    print("\n🎉 所有服务连接正常!")
    
    # 启动Gradio
    launch_gradio()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        traceback.print_exc()

# AppAgentX 启动指南

## ✅ 配置完成状态

### 已完成的配置：

1. **✅ LLM配置 (Qwen)**
   - API URL: `https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions`
   - API Key: `sk-a087535b2bc749f1aee28526cd151e7a`
   - 模型: `qwen-plus-2025-07-28`

2. **✅ Neo4j Aura云数据库**
   - URI: `neo4j+s://0cc5ef01.databases.neo4j.io`
   - 用户名: `neo4j`
   - 密码: `wsyhTX22M3gPYN2yLIg4qhDdiUKeDp-GQvnwzrFFyBo`

3. **✅ ADB配置**
   - 路径: `C:\apps\adb\platform-tools\adb.exe`
   - 设备已连接: `27a83c9c`

4. **✅ Python依赖**
   - 所有requirements.txt中的依赖已安装完成

## 🚀 启动步骤

### 方法1: 直接启动 (推荐)

在项目根目录 `E:\code\AppAgentX` 中打开命令行，运行：

```bash
python demo.py
```

### 方法2: 使用启动脚本

```bash
python start_appagentx.py
```

## 📱 使用流程

1. **启动成功后**，会显示类似以下信息：
   ```
   Running on local URL:  http://127.0.0.1:7860
   Running on public URL: https://xxxxx.gradio.live
   ```

2. **在浏览器中打开显示的URL**

3. **使用界面**：
   - **初始化页面**: 选择设备 `27a83c9c`，输入任务描述
   - **自动探索**: AI自动执行任务
   - **用户探索**: 手动控制设备操作
   - **链理解与演化**: 分析和优化操作序列

## ⚠️ 注意事项

### 必需服务：
- ✅ Neo4j Aura (已配置)
- ✅ Qwen LLM (已配置)  
- ✅ Android设备 (已连接)

### 可选服务：
- ⚪ Docker后端服务 (端口8000, 8001)
  - 如果没有启动，某些高级功能可能不可用
  - 基本功能仍然可以正常使用

### 缺少的配置：
- ⚪ Pinecone API密钥 (向量数据库)
  - 可以稍后获取: https://www.pinecone.io
  - 不影响基本功能测试

## 🔧 故障排除

### 如果启动失败：

1. **检查Python环境**：
   ```bash
   python --version  # 应该是Python 3.8+
   ```

2. **检查依赖**：
   ```bash
   pip list | grep gradio
   pip list | grep langchain
   ```

3. **检查配置文件**：
   ```bash
   python -c "import config; print('Config OK')"
   ```

4. **测试Neo4j连接**：
   ```bash
   python -c "from data.graph_db import Neo4jDatabase; import config; db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH); print('Neo4j OK')"
   ```

5. **测试ADB连接**：
   ```bash
   "C:\apps\adb\platform-tools\adb.exe" devices
   ```

## 🎯 首次使用建议

1. **从简单任务开始**：
   - "打开设置"
   - "查看电池信息"
   - "打开计算器"

2. **逐步尝试复杂任务**：
   - "发送一条短信给联系人"
   - "在应用商店搜索某个应用"

3. **观察学习过程**：
   - 查看AI的操作日志
   - 理解操作链的演化过程

## 📞 技术支持

如果遇到问题：
1. 检查上述故障排除步骤
2. 查看终端错误信息
3. 确认所有必需服务正常运行

---

**🎉 配置已完成，可以开始使用AppAgentX了！**

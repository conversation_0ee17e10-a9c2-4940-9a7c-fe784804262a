#!/usr/bin/env python3
"""
Neo4j连接诊断脚本
"""

import sys
import traceback
import socket
import ssl
from urllib.parse import urlparse

def test_network_connectivity():
    """测试网络连接"""
    print("🔍 测试网络连接...")
    
    try:
        import config
        uri = config.Neo4j_URI
        print(f"   URI: {uri}")
        
        # 解析URI
        parsed = urlparse(uri)
        host = parsed.hostname
        port = parsed.port or 7687
        
        print(f"   主机: {host}")
        print(f"   端口: {port}")
        
        # 测试TCP连接
        print("🔗 测试TCP连接...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✅ TCP连接成功")
            return True
        else:
            print(f"❌ TCP连接失败，错误代码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 网络测试失败: {str(e)}")
        return False

def test_ssl_connection():
    """测试SSL连接"""
    print("\n🔒 测试SSL连接...")
    
    try:
        import config
        uri = config.Neo4j_URI
        parsed = urlparse(uri)
        host = parsed.hostname
        port = parsed.port or 7687
        
        # 创建SSL上下文
        context = ssl.create_default_context()
        
        # 测试SSL连接
        with socket.create_connection((host, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=host) as ssock:
                print(f"✅ SSL连接成功")
                print(f"   SSL版本: {ssock.version()}")
                print(f"   证书主题: {ssock.getpeercert()['subject']}")
                return True
                
    except Exception as e:
        print(f"❌ SSL连接失败: {str(e)}")
        return False

def test_neo4j_driver():
    """测试Neo4j驱动连接"""
    print("\n🔧 测试Neo4j驱动连接...")
    
    try:
        import config
        from neo4j import GraphDatabase
        
        print("1. 创建驱动...")
        driver = GraphDatabase.driver(config.Neo4j_URI, auth=config.Neo4j_AUTH)
        print("✅ 驱动创建成功")
        
        print("2. 验证连接...")
        driver.verify_connectivity()
        print("✅ 连接验证成功")
        
        print("3. 测试查询...")
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j' as message")
            record = result.single()
            print(f"✅ 查询成功: {record['message']}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Neo4j驱动测试失败: {str(e)}")
        print("🔍 详细错误:")
        traceback.print_exc()
        return False

def test_alternative_connection():
    """测试替代连接方式"""
    print("\n🔄 测试替代连接方式...")
    
    try:
        import config
        from neo4j import GraphDatabase
        
        # 尝试不同的连接配置
        configs = [
            {
                "uri": config.Neo4j_URI,
                "auth": config.Neo4j_AUTH,
                "encrypted": True,
                "trust": "TRUST_SYSTEM_CA_SIGNED_CERTIFICATES"
            },
            {
                "uri": config.Neo4j_URI,
                "auth": config.Neo4j_AUTH,
                "encrypted": True,
                "trust": "TRUST_ALL_CERTIFICATES"
            },
            {
                "uri": config.Neo4j_URI.replace("neo4j+s://", "bolt+s://"),
                "auth": config.Neo4j_AUTH,
                "encrypted": True
            }
        ]
        
        for i, cfg in enumerate(configs, 1):
            print(f"\n尝试配置 {i}:")
            print(f"   URI: {cfg['uri']}")
            
            try:
                driver = GraphDatabase.driver(
                    cfg["uri"], 
                    auth=cfg["auth"],
                    encrypted=cfg.get("encrypted", True),
                    trust=cfg.get("trust")
                )
                driver.verify_connectivity()
                print(f"✅ 配置 {i} 连接成功!")
                driver.close()
                return True
            except Exception as e:
                print(f"❌ 配置 {i} 失败: {str(e)}")
                
        return False
        
    except Exception as e:
        print(f"❌ 替代连接测试失败: {str(e)}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 可能的解决方案:")
    print("1. 检查网络连接是否正常")
    print("2. 确认Neo4j Aura实例是否正在运行")
    print("3. 检查防火墙设置")
    print("4. 验证认证信息是否正确")
    print("5. 尝试重启Neo4j Aura实例")
    print("6. 检查IP白名单设置")
    print("\n🔧 临时解决方案:")
    print("- 可以使用本地Neo4j实例进行测试")
    print("- 或者暂时禁用Neo4j功能继续使用其他功能")

def main():
    """主函数"""
    print("🔍 Neo4j连接诊断工具")
    print("=" * 50)
    
    results = []
    
    # 测试网络连接
    results.append(("网络连接", test_network_connectivity()))
    
    # 测试SSL连接
    results.append(("SSL连接", test_ssl_connection()))
    
    # 测试Neo4j驱动
    results.append(("Neo4j驱动", test_neo4j_driver()))
    
    # 如果基本连接失败，尝试替代方案
    if not results[-1][1]:
        results.append(("替代连接", test_alternative_connection()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 诊断结果:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if not all_passed:
        suggest_solutions()
    else:
        print("\n🎉 所有测试通过！Neo4j连接正常")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断诊断")
    except Exception as e:
        print(f"\n❌ 诊断过程出错: {e}")
        traceback.print_exc()

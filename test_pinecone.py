#!/usr/bin/env python3
"""
测试Pinecone连接
"""

import sys
import traceback

def test_pinecone():
    """测试Pinecone连接"""
    print("🔍 测试Pinecone向量数据库连接...")
    
    try:
        import config
        from data.vector_db import VectorStore
        
        print(f"   API Key: {config.PINECONE_API_KEY[:20]}...")
        
        # 初始化VectorStore
        print("🔗 初始化VectorStore...")
        vector_store = VectorStore(
            api_key=config.PINECONE_API_KEY,
            dimension=2048,
            batch_size=2,
        )
        
        print("✅ Pinecone向量数据库连接成功!")
        
        # 获取统计信息
        print("📊 获取索引统计信息...")
        stats = vector_store.get_stats()
        print(f"   索引统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pinecone连接失败: {str(e)}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        return False

def test_all_services():
    """测试所有服务"""
    print("🎯 完整服务连接测试")
    print("=" * 50)
    
    results = []
    
    # 测试Qwen
    print("🤖 测试Qwen LLM...")
    try:
        import config
        from langchain_openai import ChatOpenAI
        from pydantic import SecretStr
        
        model = ChatOpenAI(
            openai_api_base=config.LLM_BASE_URL,
            openai_api_key=SecretStr(config.LLM_API_KEY),
            model_name=config.LLM_MODEL,
            max_tokens=50,
        )
        response = model.invoke("测试")
        print("✅ Qwen LLM连接成功")
        results.append(("Qwen LLM", True))
    except Exception as e:
        print(f"❌ Qwen LLM连接失败: {str(e)}")
        results.append(("Qwen LLM", False))
    
    # 测试Neo4j
    print("\n🗄️ 测试Neo4j数据库...")
    try:
        from data.graph_db import Neo4jDatabase
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        print("✅ Neo4j连接成功")
        db.close()
        results.append(("Neo4j", True))
    except Exception as e:
        print(f"❌ Neo4j连接失败: {str(e)}")
        results.append(("Neo4j", False))
    
    # 测试ADB
    print("\n📱 测试ADB设备...")
    try:
        from tool.screen_content import list_all_devices
        devices = list_all_devices()
        if devices:
            print(f"✅ ADB连接成功，设备: {devices}")
            results.append(("ADB设备", True))
        else:
            print("❌ 未发现ADB设备")
            results.append(("ADB设备", False))
    except Exception as e:
        print(f"❌ ADB连接失败: {str(e)}")
        results.append(("ADB设备", False))
    
    # 测试Pinecone
    print("\n🔍 测试Pinecone向量数据库...")
    pinecone_result = test_pinecone()
    results.append(("Pinecone", pinecone_result))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for service, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {service}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有服务连接正常!")
        print("\n🚀 可以启动完整版AppAgentX了:")
        print("   python demo.py")
    else:
        print("⚠️ 部分服务连接失败")
    
    return all_passed

if __name__ == "__main__":
    test_all_services()

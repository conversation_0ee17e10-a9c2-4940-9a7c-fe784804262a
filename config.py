# LLM Configuration
# These settings control the connection and behavior of the Large Language Model API
# Using Alibaba Cloud Qwen API

LLM_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
# Base URL for the Qwen API service from Alibaba Cloud

LLM_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"
# API key for authentication with the Qwen service

LLM_MODEL = "qwen-plus-2025-07-28"
# Qwen model version to be used for inference

LLM_MAX_TOKEN = 1500
# Maximum number of tokens allowed in a single LLM request

LLM_REQUEST_TIMEOUT = 500
# Timeout in seconds for LLM API requests

LLM_MAX_RETRIES = 3
# Maximum number of retry attempts for failed LLM API calls

# LangChain Configuration
# Settings for LangChain integration and monitoring
# Uncomment and fill in the following settings if you need LangSmith functionality

LANGCHAIN_TRACING_V2 = "false"
# Enables LangSmith tracing for debugging and monitoring

LANGCHAIN_ENDPOINT = "https://api.smith.langchain.com"
# Endpoint URL for LangSmith API services

LANGCHAIN_API_KEY = "lsv2_"
# API key for authentication with LangSmith services
# Please enter your LangSmith API key here if needed

LANGCHAIN_PROJECT = "xxx"
# Project name for organizing LangSmith resources

# Neo4j Configuration
# Settings for connecting to the Neo4j Aura cloud database

Neo4j_URI = "neo4j+s://0cc5ef01.databases.neo4j.io"
# URI for connecting to the Neo4j Aura cloud database

Neo4j_AUTH = ("neo4j", "wsyhTX22M3gPYN2yLIg4qhDdiUKeDp-GQvnwzrFFyBo")
# Authentication credentials for Neo4j Aura cloud service

# Feature Extractor Configuration
# Settings for the feature extraction service
# Please ensure this service is running at the specified address

Feature_URI = "http://127.0.0.1:8001"
# URI for the feature extraction service API
# Default is localhost port 8001, update if needed

# Screen Parser Configuration
# Settings for the screen parsing service
# Please ensure this service is running at the specified address

Omni_URI = "http://127.0.0.1:8000"
# URI for the Omni screen parsing service API
# Default is localhost port 8000, update if needed

# Vector Storage Configuration
# Settings for the vector database used for embeddings storage
# Please fill in your vector database information

PINECONE_API_KEY = "pcsk_2UXXjm_7EApfMK8eX7YjxgCwUtoCQMWV6DvQ1P39jwFczKqAg4N4jxuvhYZ5XvyGrU8Wrq"
# API key for authentication with Pinecone vector database service

# ADB Configuration
# Settings for Android Debug Bridge
ADB_PATH = r"C:\apps\adb\platform-tools\adb.exe"
# Full path to the ADB executable

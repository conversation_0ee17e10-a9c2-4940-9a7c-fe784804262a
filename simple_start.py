#!/usr/bin/env python3
"""
简化的AppAgentX启动脚本
"""

import sys
import traceback

def main():
    print("🚀 启动AppAgentX...")
    
    try:
        # 测试基本导入
        print("📦 导入基础模块...")
        import config
        import gradio as gr
        print("✅ 基础模块导入成功")
        
        # 测试数据库连接
        print("🔗 测试数据库连接...")
        from data.graph_db import Neo4jDatabase
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        print("✅ Neo4j连接成功")
        db.close()
        
        # 测试LLM连接
        print("🤖 测试LLM连接...")
        from langchain_openai import ChatOpenAI
        from pydantic import SecretStr
        
        model = ChatOpenAI(
            openai_api_base=config.LLM_BASE_URL,
            openai_api_key=SecretStr(config.LLM_API_KEY),
            model_name=config.LLM_MODEL,
            max_tokens=50,
        )
        print("✅ LLM连接成功")
        
        # 导入核心模块
        print("📱 导入核心模块...")
        from tool.screen_content import list_all_devices
        devices = list_all_devices()
        print(f"✅ ADB设备: {devices}")
        
        # 尝试导入demo模块
        print("🎯 导入demo模块...")
        import demo
        print("✅ Demo模块导入成功")
        
        print("\n🎉 所有模块加载成功!")
        print("📱 Gradio界面应该正在启动...")
        print("🌐 请在浏览器中访问显示的URL")
        
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        
        print("\n💡 可能的解决方案:")
        print("1. 检查所有依赖是否正确安装")
        print("2. 确认配置文件是否正确")
        print("3. 检查网络连接")
        print("4. 确认设备连接状态")

if __name__ == "__main__":
    main()
